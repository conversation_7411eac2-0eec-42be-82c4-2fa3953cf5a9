package com.bugu.station.portal.config

import com.bugu.station.portal.interceptor.PortalStationInterceptor
import com.bugu.station.portal.interceptor.PortalTokenInterceptor
import com.bugu.station.portal.resolver.PorTalParamResolver
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.context.annotation.Configuration
import org.springframework.core.task.AsyncTaskExecutor
import org.springframework.web.method.support.HandlerMethodArgumentResolver
import org.springframework.web.servlet.config.annotation.AsyncSupportConfigurer
import org.springframework.web.servlet.config.annotation.CorsRegistry
import org.springframework.web.servlet.config.annotation.InterceptorRegistry
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer

import java.util.concurrent.Executors

@Configuration("portalWebConfig")
class WebConfig implements WebMvcConfigurer {



    private final PorTalParamResolver paramResolver
    private final PortalTokenInterceptor portalTokenInterceptor


    @Autowired
    WebConfig(PorTalParamResolver paramResolver , PortalTokenInterceptor portalTokenInterceptor) {
        this.paramResolver = paramResolver
        this.portalTokenInterceptor = portalTokenInterceptor
    }

    private final String[] asyncPathPatterns = ["/api/stationGroup/1.0/portal/application"]
//    private final String[] tokenExcludePathPatterns = ["/api/stationGroup/1.0/user/**", "/login", "/swagger-ui/**", "/v3/api-docs/**", "/actuator", "/error"]


    @Override
    void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(portalTokenInterceptor)
                .order(5)
                .addPathPatterns("/**") // 拦截所有请求
                .excludePathPatterns(asyncPathPatterns)


    }

    @Override
    void configureAsyncSupport(AsyncSupportConfigurer configurer) {
        // 配置异步处理的超时时间(毫秒)
        configurer.setDefaultTimeout(5000)
        // 配置异步任务执行器
        configurer.setTaskExecutor(Executors.newFixedThreadPool(10) as AsyncTaskExecutor)
    }

    @Override
    void addArgumentResolvers(List<HandlerMethodArgumentResolver> resolvers) {
        resolvers.add(paramResolver)
    }

    @Override
    void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/api/**")
                .allowedOrigins("http://codeless.yunzhiyuan100.com", "https://daily.buguk12.com", "https://bg-digtital-edu.ts.ixaedu.cn") // 前端地址
                .allowedMethods("GET", "POST", "PUT", "DELETE", "OPTIONS")
                .allowedHeaders("*")
                .exposedHeaders("Authorization")
                .allowCredentials(true)
                .maxAge(3600)
    }
}