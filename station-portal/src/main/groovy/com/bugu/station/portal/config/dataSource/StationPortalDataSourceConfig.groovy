package com.bugu.station.portal.config.dataSource

import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.boot.orm.jpa.EntityManagerFactoryBuilder
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.context.annotation.Primary
import org.springframework.data.jpa.repository.config.EnableJpaRepositories
import org.springframework.orm.jpa.JpaTransactionManager
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean
import org.springframework.transaction.PlatformTransactionManager
import org.springframework.transaction.annotation.EnableTransactionManagement

import javax.sql.DataSource

@Configuration
@EnableJpaRepositories(basePackages = "com.bugu.station.portal.repository",
        entityManagerFactoryRef = "stationPortalEntityManagerFactory",
        transactionManagerRef="stationPortalTransactionManager"
)
@EnableTransactionManagement(proxyTargetClass = true)
class StationPortalDataSourceConfig {


    @Bean(name = "stationPortalEntityManagerFactory")
    LocalContainerEntityManagerFactoryBean entityPortalManagerFactory(EntityManagerFactoryBuilder builder,
                                                                      @Qualifier("stationPortalDataSource") DataSource dataSource) {
        return builder.dataSource(dataSource)
                .packages("com.bugu.station.portal.domain")
                .persistenceUnit("stationPortal")
                .build()
    }


    @Bean(name = "stationPortalTransactionManager")
    PlatformTransactionManager stationPortalTransactionManager(@Qualifier("stationPortalEntityManagerFactory") LocalContainerEntityManagerFactoryBean entityPortalManagerFactory) {
        return new JpaTransactionManager(entityPortalManagerFactory.getObject())
    }
}
