package com.bugu.station.portal.resolver

import com.bugu.station.portal.resolver.date.DateTimeParam
import com.bugu.station.portal.resolver.date.DateTimeRequest
import com.bugu.station.portal.resolver.page.PageParam
import com.bugu.station.portal.resolver.page.PageRequest
import groovy.util.logging.Slf4j
import org.springframework.core.MethodParameter
import org.springframework.stereotype.Component
import org.springframework.web.bind.support.WebDataBinderFactory
import org.springframework.web.context.request.NativeWebRequest
import org.springframework.web.method.support.HandlerMethodArgumentResolver
import org.springframework.web.method.support.ModelAndViewContainer

/**
 * @Title: PageParamResolver
 * @Author: [sunnysails]
 * @Package com.bugu.station.api.resolver
 * @Date: 2025/6/27 18:39
 * @Description: ${description}
 */
@Slf4j
@Component
class PorTalParamResolver implements HandlerMethodArgumentResolver {
    private static final PAGE_PARAMS = ['page', 'pageNo', 'pageNum', 'current', 'p']
    private static final SIZE_PARAMS = ['size', 'pageSize', 'limit', 'rows', 's']
    private static final TIME_PARAMS = ['timestamp', 'startDate', 'endDate', 'startTime', 'endTime']

    @Override
    boolean supportsParameter(MethodParameter parameter) {
        parameter.hasParameterAnnotation(PageParam.class) ||
                parameter.hasParameterAnnotation(DateTimeParam.class)
    }

    @Override
    Object resolveArgument(MethodParameter parameter, ModelAndViewContainer mavContainer,
                           NativeWebRequest webRequest, WebDataBinderFactory binderFactory) {


        if (parameter.hasParameterAnnotation(PageParam.class)) {
            int page = findParamValue(webRequest, PAGE_PARAMS, 1)
            int size = findParamValue(webRequest, SIZE_PARAMS, 20)
            return new PageRequest(page, size)
        }

        if (parameter.hasParameterAnnotation(DateTimeParam.class)) {
            // 假设 TIME_PARAMS 是 ['timestamp', 'startDate', 'endDate', 'startTime', 'endTime']
            Map<String, Long> timeParams = [:]
            for (name in TIME_PARAMS) {
                String value = webRequest.getParameter(name)
                if (value) {
                    try {
                        timeParams[name] = value.toLong()
                    } catch (NumberFormatException e) {
                        log.warn("Invalid timestamp format for parameter: ${name}=${value}")
                    }
                }
            }
            return new DateTimeRequest(timeParams)
        }

        return null
    }

    private static int findParamValue(NativeWebRequest request, List<String> paramNames, int defaultValue) {
        for (name in paramNames) {
            String value = request.getParameter(name)
            if (value) {
                return value.toInteger()
            }
        }
        return defaultValue
    }
}
