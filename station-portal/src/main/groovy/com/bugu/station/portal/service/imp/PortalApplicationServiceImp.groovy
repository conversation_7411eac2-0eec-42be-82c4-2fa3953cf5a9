package com.bugu.station.portal.service.imp

import com.bugu.result.List2VM
import com.bugu.station.common.constant.EntityStatusEnum
import com.bugu.station.portal.domain.ApplicationEntity
import com.bugu.station.portal.model.ApplicationDto
import com.bugu.station.portal.repository.PortalApplicationEntityRepository
import com.bugu.station.portal.service.PortalApplicationService
import com.bugu.usermodule.clientapi.service.OrganizationRpcService
import com.bugu.usermodule.clientapi.vo.org.OrganizationRpcVO
import org.apache.dubbo.config.annotation.DubboReference
import org.springframework.beans.factory.annotation.Value
import org.springframework.data.domain.Page
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Pageable
import org.springframework.data.domain.Sort
import org.springframework.stereotype.Service


/**
 * @Author: sjl
 * @CreateTime: 2025-08-06
 * @Description: ${description}
 * @Version: 1.0
 */
@Service
class PortalApplicationServiceImp implements PortalApplicationService{
    private final PortalApplicationEntityRepository portalApplicationEntityRepository

    PortalApplicationServiceImp(PortalApplicationEntityRepository portalApplicationEntityRepository) {
        this.portalApplicationEntityRepository = portalApplicationEntityRepository
    }

    @DubboReference
    OrganizationRpcService organizationRpcService

    @Value('${station.sync.lesseeId}')
    String lesseeId

    @Override
    List2VM< ApplicationDto> getList(int p, int  s) {
        OrganizationRpcVO rpcVO = organizationRpcService.transformOrganizationTree(Long.valueOf(lesseeId))
        List<String> allOrganizationIds = getAllOrganizationIds(rpcVO)
        List<Long> orgIds = []
        allOrganizationIds.each { organizationId ->
            orgIds.add(Long.valueOf(organizationId))
        }
        Sort sort = Sort.by(
                Sort.Order.desc("id") // 或者 asc，根据业务需求决定
        )
        Pageable pageable = PageRequest.of((p - 1), s, sort)
//        Page<ApplicationEntity> entityPage = portalApplicationEntityRepository.findAllPage(orgIds, EntityStatusEnum.NORMAL.status, pageable)
        Page<ApplicationEntity> entityPage = portalApplicationEntityRepository.findAllBySyncTypeAndStatus(EntityStatusEnum.NORMAL.status, pageable)

        if (entityPage.getContent().isEmpty()) {
            return List2VM< ApplicationDto>.empty()
        } else {
            List<ApplicationEntity> entities = entityPage.getContent()
            List<ApplicationDto> dtos = new ArrayList<ApplicationDto>()
            entities.each { entity ->
                dtos.add(new ApplicationDto(
                        pic: entity.pic,
                        applet_url: entity.appletUrl,
                        client_url: entity.clientUrl,
                        client_pc_url: entity.clientPcUrl,
                        name: entity.name,
                        status: entity.status,
                        memo: entity.memo
                ))
            }
            return new List2VM<ApplicationDto>(dtos, entityPage.getTotalElements(), p, s)
        }

    }

    List<String> getAllOrganizationIds(OrganizationRpcVO org) {
        [org.id] + (org.children?.collectMany { getAllOrganizationIds(it) } ?: [])
    }
}
