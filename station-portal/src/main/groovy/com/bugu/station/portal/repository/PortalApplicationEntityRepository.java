package com.bugu.station.portal.repository;

import com.bugu.station.portal.domain.ApplicationEntity;
import com.bugu.station.portal.repository.base.BaseJpaRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.ArrayList;
import java.util.List;

public interface PortalApplicationEntityRepository extends BaseJpaRepository<ApplicationEntity, Long> {

    List<ApplicationEntity> findAllByTypesAndStatus(Integer types, Byte status);

    @Query("SELECT n FROM ApplicationEntity n " +
            "WHERE n.organizationId IN :orgIdList " +
            "AND n.status = :status " +
            "AND n.syncType = 3")
    Page<ApplicationEntity> findAllPage(@Param("orgIdList") List<Long> orgIdList,
                                        @Param("status") Integer status,
                                        Pageable pageable);
}
