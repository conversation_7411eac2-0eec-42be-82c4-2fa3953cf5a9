package com.bugu.station.api.interceptor

import groovy.util.logging.Slf4j
import jakarta.servlet.http.HttpServletRequest
import jakarta.servlet.http.HttpServletResponse
import org.springframework.stereotype.Component
import org.springframework.web.context.request.RequestContextHolder
import org.springframework.web.context.request.ServletRequestAttributes
import org.springframework.web.servlet.HandlerInterceptor

/**
 * @Title: StationInterceptor
 * @Author: [sunnysails]
 * @Package com.bugu.station.api.interceptor
 * @Date: 2025/7/7 20:54
 * @Description: 无需token接口拦截器，处理站点信息
 */
@Slf4j
@Component
class StationInterceptor implements HandlerInterceptor {

    @Override
    boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
//        String[] orgIds = request.getHeader("organizationId")
        String[] orgIds = request.getParameterMap().get("station")
        if (orgIds) {
            switch (orgIds[0]) {
                case "school":
                    request.setAttribute("orgId", "school")
                    break
                case "district":
                    request.setAttribute("orgId", "district")
                    break
                case "department":
                    request.setAttribute("orgId", "department")
                    break
                default:
                    request.setAttribute("orgId", "1234")
                    break
            }
        } else {
            request.setAttribute("orgId", "1234")
        }
        RequestContextHolder.setRequestAttributes(new ServletRequestAttributes(request))

        return true
    }
}
