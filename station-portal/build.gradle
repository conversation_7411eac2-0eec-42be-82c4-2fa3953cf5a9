buildscript {
    repositories {
        mavenLocal()
//        mavenCentral()
        maven { url "https://maven.aliyun.com/repository/gradle-plugin" }
        maven { url "https://maven.aliyun.com/repository/grails-core" }
        maven {
            credentials {
                username '5ee057b297528f50b957a24b'
                password '5EDW9X0yEh(z'
            }
            url 'https://packages.aliyun.com/maven/repository/2031436-release-DZ8BKk/'
        }
        maven {
            credentials {
                username '5ee057b297528f50b957a24b'
                password '5EDW9X0yEh(z'
            }
            url 'https://packages.aliyun.com/maven/repository/2031436-snapshot-ROy4tH/'
        }
    }
}

plugins {
    id 'java'
    id 'groovy'
    id 'org.springframework.boot' version '3.5.3'
    id 'io.spring.dependency-management' version '1.1.7'
}

group = 'com.bugu'
version = '0.0.1-SNAPSHOT'

java {
    toolchain {
        languageVersion = JavaLanguageVersion.of(17)
    }
}

configurations {
    compileOnly {
        extendsFrom annotationProcessor
    }
}

repositories {
//    mavenLocal()
    maven { url "https://maven.aliyun.com/repository/jcenter" }
    maven { url "https://maven.aliyun.com/repository/public" }
    maven { url "https://maven.aliyun.com/repository/google" }
    maven { url "https://maven.aliyun.com/repository/gradle-plugin" }
    maven { url "https://maven.aliyun.com/repository/spring" }
    maven { url "https://maven.aliyun.com/repository/spring-plugin" }
    maven {
        credentials {
            username '5ee057b297528f50b957a24b'
            password '5EDW9X0yEh(z'
        }
        url 'https://packages.aliyun.com/maven/repository/2031436-release-DZ8BKk/'
    }
    maven {
        credentials {
            username '5ee057b297528f50b957a24b'
            password '5EDW9X0yEh(z'
        }
        url 'https://packages.aliyun.com/maven/repository/2031436-snapshot-ROy4tH/'
    }
    mavenCentral()
}

ext {
    set('springCloudVersion', "2025.0.0")
    set("springCloudAlibabaVersion", "2023.0.3.3")
    set("mysqlConnectorJavaVersion", "8.3.0")
    set("mybatisSpringBootStarterVersion", "3.0.3")
    set("mybatisSpringVersion", "2.1.1")
    set("mybatisVersion", "3.5.13")
    set("mapstructVersion", "1.6.3")
    set("guavaVersion", "33.1.0-jre")
    set("commonsLang3Version", "3.14.0")
    set("httpclient5Version", "5.3.1")
    set("fastjsonVersion", "2.0.51")
    set("quartzVersion", "2.3.2")
    set("postgresqlVersion", "42.7.3")
    set("jodaTimeVersion", "2.12.7")
    set("dingtalkVersion", "2.1.26")
    set("rxjavaVersion", "3.1.8")
    set("recordBuilderVersion", "41")
    set("groovyVersion", "4.0.26")
    set("dubboSpringBootStarterVersion", "3.2.14")
    set("aliyunSdkOssVersion", "3.10.2")
    set("jetCacheVersion", "2.7.7")
    set("kryo5Version", "5.6.2")
}

dependencyManagement {
    imports {
        mavenBom "org.springframework.cloud:spring-cloud-dependencies:${springCloudVersion}"
        mavenBom "com.alibaba.cloud:spring-cloud-alibaba-dependencies:${springCloudAlibabaVersion}"
    }
    applyMavenExclusions false
}

dependencies {

    implementation project(':station-common')

    implementation 'jakarta.annotation:jakarta.annotation-api:3.0.0'
    implementation 'org.springframework.boot:spring-boot-starter-data-jdbc'
    implementation 'org.springframework.boot:spring-boot-starter-data-jpa'
    implementation 'org.springframework.boot:spring-boot-starter-jdbc'
    implementation 'org.springframework.boot:spring-boot-starter-validation'
    implementation 'org.apache.commons:commons-pool2' // 连接池
    implementation 'org.apache.groovy:groovy'
    implementation 'com.hankcs:hanlp:portable-1.8.6'
    implementation "org.mapstruct:mapstruct:${mapstructVersion}"
    implementation "com.bugu:user-module-client:0.4.2"
    implementation 'io.swagger.core.v3:swagger-annotations:2.2.30'
    implementation 'com.fasterxml.jackson.core:jackson-annotations:2.19.1'
    implementation 'org.springdoc:springdoc-openapi-starter-webmvc-ui:2.8.9'
    implementation "org.apache.dubbo:dubbo-spring-boot-starter:${dubboSpringBootStarterVersion}"
    implementation 'io.jsonwebtoken:jjwt-api:0.11.5'
    runtimeOnly 'io.jsonwebtoken:jjwt-impl:0.11.5'
    runtimeOnly 'io.jsonwebtoken:jjwt-jackson:0.11.5'
    compileOnly 'org.projectlombok:lombok'
    runtimeOnly 'com.mysql:mysql-connector-j'
    annotationProcessor 'org.projectlombok:lombok'
    annotationProcessor "org.mapstruct:mapstruct-processor:${mapstructVersion}"
    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    testRuntimeOnly 'org.junit.platform:junit-platform-launcher'
}

test {
    useJUnitPlatform()
}

// 配置注解处理器选项
tasks.withType(JavaCompile).configureEach {
    options.compilerArgs += ['-Amapstruct.defaultComponentModel=spring',
                             '-Amapstruct.unmappedTargetPolicy=IGNORE',
                             '-Amapstruct.suppressGeneratorTimestamp=true',
                             '-Amapstruct.suppressGeneratorVersionInfoComment=true',
                             '-Amapstruct.verbose=true',
                             '-parameters' // 保留参数名信息
    ]
    options.encoding = 'UTF-8'
}
// 对于Groovy编译任务也需要配置
tasks.withType(GroovyCompile).configureEach {
    groovyOptions.javaAnnotationProcessing = true // 启用注解处理
    options.compilerArgs += ['-parameters' // Groovy编译也保留参数名
    ]
}
// 确保 clean 任务删除生成的文件
clean {
    delete fileTree(dir: 'build/generated/sources/annotationProcessor', include: '**/*')
}


bootJar {
    duplicatesStrategy = DuplicatesStrategy.EXCLUDE
}
