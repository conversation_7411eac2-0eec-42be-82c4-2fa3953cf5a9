package com.bugu.station.core.repository.site;

import com.bugu.station.core.domain.site.SiteConfigEntity;
import com.bugu.station.core.repository.base.BaseJpaRepository;

import java.util.List;

public interface SiteConfigEntityRepository extends BaseJpaRepository<SiteConfigEntity, Long> {

    SiteConfigEntity findFirstByOrgIdAndIsActiveAndStatus(String orgId, Boolean isActive, Integer status);

    List<SiteConfigEntity> findAllByOrgIdAndStatus(String orgId, Integer status);

    List<SiteConfigEntity> findAllByStatus(Integer status);

    List<SiteConfigEntity> findAllByOrgIdListAndStatus();
}