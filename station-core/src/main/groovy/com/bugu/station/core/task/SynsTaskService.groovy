package com.bugu.station.core.task

import com.bugu.station.common.constant.EntityStatusEnum
import com.bugu.station.common.constant.SiteConfigType
import com.bugu.station.core.domain.site.SiteConfigEntity
import com.bugu.station.core.domain.site.SiteConfigItemEntity
import com.bugu.station.core.repository.site.SiteConfigEntityRepository
import com.bugu.station.core.repository.site.SiteConfigItemEntityRepository
import com.bugu.usermodule.clientapi.service.OrganizationRpcService
import com.bugu.usermodule.clientapi.service.UserRpcService
import com.bugu.usermodule.clientapi.vo.org.OrganizationDTO
import groovy.util.logging.Slf4j
import org.apache.dubbo.config.annotation.DubboReference
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional


/**
 * @Author: sjl
 * @CreateTime: 2025-08-04
 * @Description: 同步任务服务
 * @Version: 1.0
 */
@Slf4j
@Service
@Transactional
class SynsTaskService {

    @DubboReference
    private OrganizationRpcService organizationRpcService

    @Autowired
    private SiteConfigEntityRepository siteConfigEntityRepository

    @Autowired
    private SiteConfigItemEntityRepository siteConfigItemEntityRepository

    /**
     * 同步组织站点信息
     */
    void synOrg2Site() {
        log.info("开始执行组织站点信息同步任务")

        try {
            // 1. 获取所有需要同步的组织列表
            List<String> organizationIds = getAllActiveOrganizations()

            if (organizationIds.isEmpty()) {
                log.info("没有找到需要同步的组织")
                return
            }

            log.info("找到 {} 个组织需要同步", organizationIds.size())

            // 2. 遍历每个组织进行同步
            int successCount = 0
            int failCount = 0

            for (String orgId : organizationIds) {
                try {
                    syncSingleOrganization(orgId)
                    successCount++
                    log.debug("组织 {} 同步成功", orgId)
                } catch (Exception e) {
                    failCount++
                    log.error("组织 {} 同步失败: {}", orgId, e.getMessage(), e)
                }
            }

            log.info("组织站点信息同步任务完成，成功: {}, 失败: {}", successCount, failCount)

        } catch (Exception e) {
            log.error("组织站点信息同步任务执行失败", e)
            throw e
        }
    }













    /**
     * 创建站点配置项
     */
    private SiteConfigItemEntity createSiteConfigItem(String orgId, Long configId,
                                                     Integer optionType, String valueData) {
        SiteConfigItemEntity item = new SiteConfigItemEntity()
        item.orgId = orgId
        item.configId = configId
        item.optionType = optionType
        item.valueData = valueData
        item.isActive = true
        item.isRequired = true
        item.status = EntityStatusEnum.NORMAL.status
        item.sortOrder = 0

        return item
    }


}
