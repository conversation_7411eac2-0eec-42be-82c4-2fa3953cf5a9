package com.bugu.station.core.task

import com.alibaba.fastjson2.JSONArray
import com.alibaba.fastjson2.JSONObject
import com.bugu.station.common.constant.ConstantEnum
import com.bugu.station.common.constant.EntityStatusEnum
import com.bugu.station.common.constant.SiteConfigType
import com.bugu.station.core.domain.site.SiteConfigEntity
import com.bugu.station.core.domain.site.SiteConfigItemEntity
import com.bugu.station.core.repository.site.SiteConfigEntityRepository
import com.bugu.station.core.repository.site.SiteConfigItemEntityRepository
import com.bugu.usermodule.clientapi.service.OrganizationRpcService
import com.bugu.usermodule.clientapi.vo.org.OrganizationRpcVO
import com.google.common.collect.HashMultimap
import groovy.util.logging.Slf4j
import org.apache.dubbo.config.annotation.DubboReference
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional


/**
 * @Author: sjl
 * @CreateTime: 2025-08-04
 * @Description: 同步任务服务
 * @Version: 1.0
 */
@Slf4j
@Service
@Transactional
class SynsTaskService {

    @DubboReference
    OrganizationRpcService organizationRpcService

    @Autowired
    SiteConfigEntityRepository siteConfigEntityRepository

    @Autowired
    SiteConfigItemEntityRepository siteConfigItemEntityRepository

    @Value('${station.sync.lesseeId}')
    private Long lesseeId

    @Value('${station.daliyUrl}')
    private String daliyUrl

    /**
     * 同步组织站点信息
     */
    def synOrg2Site() {
        log.info("开始执行组织站点信息同步任务，配置信息：批处理大小={}, 超时时间={}ms")

        try {
            // 1. 获取所有需要同步的组织列表
            OrganizationRpcVO rpcVO = organizationRpcService.transformOrganizationTree(lesseeId)
            List<String> allOrganizationIds = getAllOrganizationIds(rpcVO)

            List<OrganizationRpcVO> organizationList = flattenOrganizationTree(rpcVO)
            HashMap<Long, OrganizationRpcVO> organizationMap = new HashMap<>()
            organizationMap.putAll(organizationList.collectEntries { organization ->
                [organization.id, organization]
            })

            //过滤满足类型
            List<OrganizationRpcVO> organizations = new ArrayList<>()
            organizationList.each {
                if (it.type == ConstantEnum.OrganizationCategory.getEnumByName("教育行政机构").type) {
                    if (it.category == ConstantEnum.OrganizationType.getEnumByName("设区市教育局").type || it.type == ConstantEnum.OrganizationType.getEnumByName("县（市、区）教育局").type) {
                        organizations.add(it)
                    }
                }
                if (it.type == ConstantEnum.OrganizationCategory.getEnumByName("学校").type) {
                    organizations.add(it)
                }
            }

            if (organizations.isEmpty()) {
                log.info("没有找到需要同步的组织")
                return
            }

            log.info("找到 {} 个组织需要同步", organizations.size())


            // 2. 遍历每个组织进行同步
            int successCount = 0
            int failCount = 0
            int updateCount = 0
            int createCount = 0

            // 获取现有的站点配置，按orgId建立映射
//            List<SiteConfigEntity> existingList = findByPid(lesseeId)
            List<SiteConfigEntity> existingList = siteConfigEntityRepository.findAllByOrgIdListAndStatus(allOrganizationIds, EntityStatusEnum.NORMAL.status)
            Map<String, SiteConfigEntity> existingMap = existingList.collectEntries { siteConfigEntity ->
                [siteConfigEntity.orgId, siteConfigEntity]
            }
            log.info("已存在站点配置：{}", JSONArray.toJSONString(existingList) )

            List<SiteConfigEntity> siteConfigEntities = new ArrayList<>()
            List<SiteConfigItemEntity> siteConfigItemEntities = new ArrayList<>()

            for (OrganizationRpcVO organizationRpcVO : organizations) {
                try {
                    String orgId = organizationRpcVO.id
                    SiteConfigEntity existingEntity = existingMap.get(orgId)

                    if (existingEntity) {
                        // 更新现有站点配置
                        log.debug("更新组织 {} 的站点配置", orgId)
                        syncSingleOrganization(organizationRpcVO, existingEntity)
                        siteConfigEntities.add(existingEntity)

                        updateCount++
                    } else {
                        // 创建新的站点配置
                        log.debug("创建组织 {} 的站点配置", orgId)
                        SiteConfigEntity newEntity = syncSingleOrganization(organizationRpcVO, null)
                        siteConfigEntities.add(newEntity)

                        createCount++
                    }

                    successCount++
                } catch (Exception e) {
                    failCount++
                    log.error("组织 {} 同步失败: {}", organizationRpcVO.id, e.getMessage(), e)
                }
            }

            // 批量保存站点配置

            List<SiteConfigEntity> savedEntities = siteConfigEntityRepository.saveAll(siteConfigEntities)
            log.info("批量保存了 {} 个站点配置", siteConfigEntities.size())


            // 为新创建的站点配置创建配置项
//            for (SiteConfigEntity entity : savedEntities) {
//                if (entity.id && !existingMap.containsKey(entity.orgId)) {
//                    OrganizationRpcVO orgInfo = organizationMap.get(entity.orgId)
//                    if (orgInfo) {
//                        List<SiteConfigItemEntity> newItems = createSiteConfigItem( orgInfo,entity)
//                        siteConfigItemEntities.addAll(newItems)
//                    }
//                }
//            }
//
//            // 批量保存站点配置项
//            if (!siteConfigItemEntities.isEmpty()) {
//                siteConfigItemEntityRepository.saveAll(siteConfigItemEntities)
//                log.info("批量保存了 {} 个站点配置项", siteConfigItemEntities.size())
//            }
//
//            log.info("组织站点信息同步任务完成，总计: {}, 成功: {}, 失败: {}, 新增: {}, 更新: {}",
//                    organizations.size(), successCount, failCount, createCount, updateCount)

        } catch (Exception e) {
            log.error("组织站点信息同步任务执行失败", e)
            throw e
        }
    }

    List<OrganizationRpcVO> flattenOrganizationTree(OrganizationRpcVO org) {
        [org] + (org.children?.collectMany { flattenOrganizationTree(it) } ?: [])
    }

    List<String> getAllOrganizationIds(OrganizationRpcVO org) {
        [org.id] + (org.children?.collectMany { getAllOrganizationIds(it) } ?: [])
    }



    private SiteConfigEntity syncSingleOrganization(OrganizationRpcVO organizationRpcVO, SiteConfigEntity entity) {
        SiteConfigEntity siteConfigEntity = new SiteConfigEntity()
        if (entity) {
            entity.title = organizationRpcVO.name
            if (organizationRpcVO.type == ConstantEnum.OrganizationCategory.getEnumByName("学校").type) {
                entity.type = ConstantEnum.SiteConfigTypeEnum.SCHOOL_STATION.type
            } else if (organizationRpcVO.type == ConstantEnum.OrganizationCategory.getEnumByName("教育行政机构").type && organizationRpcVO.category == ConstantEnum.OrganizationType.getEnumByName("设区市教育局").type) {
                entity.type = ConstantEnum.SiteConfigTypeEnum.CITYSTATION.type
            } else if (organizationRpcVO.type == ConstantEnum.OrganizationCategory.getEnumByName("教育行政机构").type && organizationRpcVO.category == ConstantEnum.OrganizationType.getEnumByName("县（市、区）教育局").type) {
                entity.type = ConstantEnum.SiteConfigTypeEnum.DISTRICT_LEVEL_STATION.type
            }
            return entity
        } else {
            siteConfigEntity.title = organizationRpcVO.name
            siteConfigEntity.isActive = true
            siteConfigEntity.status = EntityStatusEnum.NORMAL.status
            siteConfigEntity.orgId = organizationRpcVO.id
            siteConfigEntity.porgId = organizationRpcVO.pid
            if (organizationRpcVO.type == ConstantEnum.OrganizationCategory.getEnumByName("学校").type) {
                siteConfigEntity.type = ConstantEnum.SiteConfigTypeEnum.SCHOOL_STATION.type
            } else if (organizationRpcVO.type == ConstantEnum.OrganizationCategory.getEnumByName("教育行政机构").type && organizationRpcVO.category == ConstantEnum.OrganizationType.getEnumByName("设区市教育局").type) {
                siteConfigEntity.type = ConstantEnum.SiteConfigTypeEnum.CITYSTATION.type
            } else if (organizationRpcVO.type == ConstantEnum.OrganizationCategory.getEnumByName("教育行政机构").type && organizationRpcVO.category == ConstantEnum.OrganizationType.getEnumByName("县（市、区）教育局").type) {
                siteConfigEntity.type = ConstantEnum.SiteConfigTypeEnum.DISTRICT_LEVEL_STATION.type
            }
        }
        return siteConfigEntity
    }


    /**
     * 创建站点配置项
     */
    List<SiteConfigItemEntity> createSiteConfigItem(OrganizationRpcVO organizationRpcVO, SiteConfigEntity siteConfigEntity) {
        List<SiteConfigItemEntity> items = new ArrayList<>();

        // 添加第一个配置项
        items.add(new SiteConfigItemEntity(
                status: EntityStatusEnum.NORMAL.status,
                configId: siteConfigEntity.id,
                optionType: SiteConfigType.SITE_NAME.code,
                valueData: organizationRpcVO.name,
                isActive: true,
                isRequired: true
        ))

        // 添加第二个配置项
        SiteConfigItemEntity item2 = new SiteConfigItemEntity(
                status: EntityStatusEnum.NORMAL.status,
                configId: siteConfigEntity.id,
                optionType: SiteConfigType.SITE_DOMAIN.code,
                isActive: true,
                isRequired: true
        )
        if (siteConfigEntity.type == ConstantEnum.SiteConfigTypeEnum.CITYSTATION.type) {
            item2.valueData = daliyUrl + "?orgId=" + organizationRpcVO.id
        } else if (siteConfigEntity.type == ConstantEnum.SiteConfigTypeEnum.DISTRICT_LEVEL_STATION.type) {
            item2.valueData = daliyUrl + "/district?orgId=" + organizationRpcVO.id
        } else if (siteConfigEntity.type == ConstantEnum.SiteConfigTypeEnum.SCHOOL_STATION.type) {
            item2.valueData = daliyUrl + "/school?orgId=" + organizationRpcVO.id
        }
        items.add(item2)
        return items
    }

    List<SiteConfigEntity> findByPid(Long pid) {
        List<SiteConfigEntity> siteConfigEntities = siteConfigEntityRepository.findAllByStatus(EntityStatusEnum.NORMAL.status)
        if (siteConfigEntities == null || siteConfigEntities.isEmpty()) {
            return new ArrayList<>()
        }
        Map<String, SiteConfigEntity> siteConfigMap = new HashMap<>();
        HashMultimap<String, SiteConfigEntity> pidSiteConfigMap = HashMultimap.create();
        siteConfigEntities.forEach(it -> {
            siteConfigMap.put(it.orgId, it);
            if (it.porgId != null) {
                pidSiteConfigMap.put(it.porgId, it);
            }

        })
        List<SiteConfigEntity> siteConfigEntityList = new ArrayList<>();
        Stack<String> stack = new Stack<>();
        Set<String> visitedIds = new HashSet<>();
        stack.push(pid as String)
        while (!stack.isEmpty()) {
            String currentId = stack.pop()
            if (visitedIds.contains(currentId)) {
                continue
            }
            SiteConfigEntity org = siteConfigMap.get(currentId)
            if (org != null) {
                siteConfigEntityList.add(org)
                visitedIds.add(currentId)

                Set<SiteConfigEntity> subSiteConfigList = pidSiteConfigMap.get(currentId )

                if (!subSiteConfigList.isEmpty()) {
                    for (SiteConfigEntity subOrg : subSiteConfigList) {
                        stack.push(subOrg.orgId)
                    }
                }
            }
        }
        return siteConfigEntityList
    }
}
