package com.bugu.station.core.task

import com.bugu.station.common.constant.EntityStatusEnum
import com.bugu.station.common.constant.SiteConfigType
import com.bugu.station.core.config.properties.SyncTaskProperties
import com.bugu.station.core.domain.site.SiteConfigEntity
import com.bugu.station.core.domain.site.SiteConfigItemEntity
import com.bugu.station.core.repository.site.SiteConfigEntityRepository
import com.bugu.station.core.repository.site.SiteConfigItemEntityRepository
import com.bugu.usermodule.clientapi.service.OrganizationRpcService
import com.bugu.usermodule.clientapi.vo.org.OrganizationRpcVO
import groovy.util.logging.Slf4j
import org.apache.dubbo.config.annotation.DubboReference
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional


/**
 * @Author: sjl
 * @CreateTime: 2025-08-04
 * @Description: 同步任务服务
 * @Version: 1.0
 */
@Slf4j

@Transactional
class SynsTaskService {

    @DubboReference
    OrganizationRpcService organizationRpcService

    SiteConfigEntityRepository siteConfigEntityRepository

    SiteConfigItemEntityRepository siteConfigItemEntityRepository

    @Value("${station.sync.lesseeId}")
    private Long lesseeId



    /**
     * 同步组织站点信息
     */
    def synOrg2Site() {
        log.info("开始执行组织站点信息同步任务，配置信息：批处理大小={}, 超时时间={}ms")

        try {
            // 1. 获取所有需要同步的组织列表
            List<OrganizationRpcVO> rpcVOS = organizationRpcService.transformOrganizationTree( lesseeId) as List<OrganizationRpcVO>

            List<OrganizationRpcVO> organizations = flattenOrganizationTree(rpcVOS)

            if (organizations.isEmpty()) {
                log.info("没有找到需要同步的组织")
                return
            }

            log.info("找到 {} 个组织需要同步", organizations.size())


            // 2. 遍历每个组织进行同步
            int successCount = 0
            int failCount = 0

            for (OrganizationRpcVO organizationRpcVO : organizations) {
                try {
                    syncSingleOrganization(organizationRpcVO)
                    successCount++
                    log.debug("组织 {} 同步成功", orgId)
                } catch (Exception e) {
                    failCount++
                    log.error("组织 {} 同步失败: {}", orgId, e.getMessage(), e)
                }
            }

            log.info("组织站点信息同步任务完成，成功: {}, 失败: {}", successCount, failCount)

        } catch (Exception e) {
            log.error("组织站点信息同步任务执行失败", e)
            throw e
        }
    }

    List<OrganizationRpcVO> flattenOrganizationTree(List<OrganizationRpcVO> voList) {
        voList.collectMany { vo ->
            [vo] + flattenOrganizationTree(vo.children ?: [])
        }
    }


    private SiteConfigEntity  syncSingleOrganization(OrganizationRpcVO organizationRpcVO) {
        SiteConfigEntity siteConfigEntity = new SiteConfigEntity()
        siteConfigEntity.title = organizationRpcVO.name
        siteConfigEntity.isActive = true
        siteConfigEntity.status = EntityStatusEnum.NORMAL.status
        siteConfigEntity.orgId = organizationRpcVO.id
        siteConfigEntity.porgId = organizationRpcVO.pid
        return siteConfigEntity
    }





    /**
     * 创建站点配置项
     */
     List<SiteConfigItemEntity> createSiteConfigItem(OrganizationRpcVO organizationRpcVO, SiteConfigEntity siteConfigEntity) {
        List<SiteConfigItemEntity> items = new ArrayList<>();

        // 添加第一个配置项
        items.add(new SiteConfigItemEntity(
                status: EntityStatusEnum.NORMAL.status,
                configId: siteConfigEntity.id,
                optionType: 400001,
                valueData: organizationRpcVO.name,
                isActive: true,
                isRequired: true
        ))

        // 添加第二个配置项
         // 添加第一个配置项
         items.add(new SiteConfigItemEntity(
                 status: EntityStatusEnum.NORMAL.status,
                 configId: siteConfigEntity.id,
                 optionType: 400002,
                 valueData: "url",
                 isActive: true,
                 isRequired: true
         ))

        return items;
    }


}
