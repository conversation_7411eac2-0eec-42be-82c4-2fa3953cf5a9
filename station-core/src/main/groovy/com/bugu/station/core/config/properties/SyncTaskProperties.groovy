package com.bugu.station.core.config.properties

import org.springframework.boot.context.properties.ConfigurationProperties
import org.springframework.stereotype.Component

/**
 * @Title: SyncTaskProperties
 * @Author: [sjl]
 * @Package com.bugu.station.core.config.properties
 * @Date: 2025-08-04
 * @Description: 同步任务配置属性
 * @Version: 1.0
 */
@Component
@ConfigurationProperties(prefix = "station.sync")
class SyncTaskProperties {
    
    /**
     * 是否启用同步任务
     */
    boolean enabled = true
    
    /**
     * 是否启用增量同步
     */
    boolean incrementalEnabled = false
    
    /**
     * 是否启用每周全量同步
     */
    boolean weeklyEnabled = true
    
    /**
     * 同步任务执行的cron表达式
     */
    String cron = "0 0 2 * * ?"
    
    /**
     * 增量同步的cron表达式
     */
    String incrementalCron = "0 0 * * * ?"
    
    /**
     * 每周全量同步的cron表达式
     */
    String weeklyCron = "0 0 1 * * SUN"
    
    /**
     * 同步超时时间（毫秒）
     */
    long timeoutMs = 300000 // 5分钟
    
    /**
     * 批处理大小
     */
    int batchSize = 100
    
    /**
     * 重试次数
     */
    int retryCount = 3
    
    /**
     * 重试间隔（毫秒）
     */
    long retryIntervalMs = 5000
    
    /**
     * 用户模块相关配置
     */
    UserModule userModule = new UserModule()
    
    static class UserModule {
        /**
         * 用户模块服务名
         */
        String serviceName = "user-module"
        
        /**
         * RPC调用超时时间
         */
        long timeoutMs = 30000
        
        /**
         * 重试次数
         */
        int retryCount = 2
    }
    
    /**
     * 数据库相关配置
     */
    Database database = new Database()
    
    static class Database {
        /**
         * 批量操作大小
         */
        int batchSize = 50
        
        /**
         * 是否启用批量更新
         */
        boolean batchUpdateEnabled = true
    }
}
