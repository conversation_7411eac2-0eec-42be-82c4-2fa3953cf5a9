package com.bugu.station.core.service.config

import com.bugu.station.core.config.properties.SyncTaskProperties
import groovy.util.logging.Slf4j
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.core.env.Environment
import org.springframework.stereotype.Service

/**
 * @Title: ConfigReaderService
 * @Author: [sjl]
 * @Package com.bugu.station.core.service.config
 * @Date: 2025-08-04
 * @Description: 演示在station-core中读取station-app配置的各种方式
 * @Version: 1.0
 */
@Slf4j
@Service
class ConfigReaderService {

    // 方法1：使用@ConfigurationProperties（推荐）
    @Autowired
    private SyncTaskProperties syncTaskProperties

    // 方法2：使用@Value注解直接读取
    @Value('${station.sync.enabled:true}')
    private boolean syncEnabled

    @Value('${station.sync.batch-size:100}')
    private int batchSize

    @Value('${station.sync.timeout-ms:300000}')
    private long timeoutMs

    @Value('${spring.application.name}')
    private String applicationName

    @Value('${server.port:8080}')
    private int serverPort

    // 方法3：使用Environment（最灵活）
    @Autowired
    private Environment environment

    /**
     * 演示使用@ConfigurationProperties读取配置
     */
    void demonstrateConfigurationProperties() {
        log.info("=== 使用@ConfigurationProperties读取配置 ===")
        log.info("同步任务启用状态: {}", syncTaskProperties.enabled)
        log.info("批处理大小: {}", syncTaskProperties.batchSize)
        log.info("超时时间: {}ms", syncTaskProperties.timeoutMs)
        log.info("重试次数: {}", syncTaskProperties.retryCount)
        log.info("用户模块服务名: {}", syncTaskProperties.userModule.serviceName)
        log.info("数据库批处理大小: {}", syncTaskProperties.database.batchSize)
    }

    /**
     * 演示使用@Value读取配置
     */
    void demonstrateValueAnnotation() {
        log.info("=== 使用@Value注解读取配置 ===")
        log.info("同步启用状态: {}", syncEnabled)
        log.info("批处理大小: {}", batchSize)
        log.info("超时时间: {}ms", timeoutMs)
        log.info("应用名称: {}", applicationName)
        log.info("服务端口: {}", serverPort)
    }

    /**
     * 演示使用Environment读取配置
     */
    void demonstrateEnvironment() {
        log.info("=== 使用Environment读取配置 ===")
        
        // 读取基本配置
        String appName = environment.getProperty("spring.application.name", "unknown")
        Integer port = environment.getProperty("server.port", Integer.class, 8080)
        
        // 读取自定义配置
        Boolean syncEnabled = environment.getProperty("station.sync.enabled", Boolean.class, true)
        String cron = environment.getProperty("station.sync.cron", "0 0 2 * * ?")
        
        // 读取数据库配置
        String dbUrl = environment.getProperty("spring.datasource.station-group.url")
        String dbUsername = environment.getProperty("spring.datasource.station-group.username")
        
        // 读取Redis配置
        String redisHost = environment.getProperty("redis.host")
        Integer redisPort = environment.getProperty("redis.port", Integer.class)
        
        log.info("应用名称: {}", appName)
        log.info("服务端口: {}", port)
        log.info("同步启用: {}", syncEnabled)
        log.info("同步Cron: {}", cron)
        log.info("数据库URL: {}", dbUrl)
        log.info("数据库用户名: {}", dbUsername)
        log.info("Redis主机: {}", redisHost)
        log.info("Redis端口: {}", redisPort)
    }

    /**
     * 动态读取配置（运行时读取）
     */
    String getDynamicConfig(String key, String defaultValue) {
        return environment.getProperty(key, defaultValue)
    }

    /**
     * 检查配置是否存在
     */
    boolean hasConfig(String key) {
        return environment.containsProperty(key)
    }

    /**
     * 获取所有激活的Profile
     */
    String[] getActiveProfiles() {
        return environment.getActiveProfiles()
    }

    /**
     * 获取同步任务配置信息
     */
    Map<String, Object> getSyncTaskConfig() {
        return [
            enabled: syncTaskProperties.enabled,
            batchSize: syncTaskProperties.batchSize,
            timeoutMs: syncTaskProperties.timeoutMs,
            retryCount: syncTaskProperties.retryCount,
            cron: syncTaskProperties.cron,
            userModuleServiceName: syncTaskProperties.userModule.serviceName,
            userModuleTimeoutMs: syncTaskProperties.userModule.timeoutMs,
            databaseBatchSize: syncTaskProperties.database.batchSize,
            databaseBatchUpdateEnabled: syncTaskProperties.database.batchUpdateEnabled
        ]
    }

    /**
     * 验证配置的有效性
     */
    boolean validateSyncConfig() {
        try {
            if (syncTaskProperties.batchSize <= 0) {
                log.error("批处理大小必须大于0，当前值: {}", syncTaskProperties.batchSize)
                return false
            }
            
            if (syncTaskProperties.timeoutMs <= 0) {
                log.error("超时时间必须大于0，当前值: {}ms", syncTaskProperties.timeoutMs)
                return false
            }
            
            if (!syncTaskProperties.cron) {
                log.error("Cron表达式不能为空")
                return false
            }
            
            log.info("同步任务配置验证通过")
            return true
            
        } catch (Exception e) {
            log.error("配置验证失败", e)
            return false
        }
    }
}
