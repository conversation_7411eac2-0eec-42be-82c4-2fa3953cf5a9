package com.bugu.station.core.config

import org.springframework.boot.context.properties.ConfigurationProperties
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler
import org.springframework.scheduling.annotation.SchedulingConfigurer
import org.springframework.scheduling.config.ScheduledTaskRegistrar

/**
 * @Title: SyncTaskConfig
 * @Author: [sjl]
 * @Package com.bugu.station.core.config
 * @Date: 2025-08-04
 * @Description: 同步任务配置类
 * @Version: 1.0
 */
@Configuration
class SyncTaskConfig implements SchedulingConfigurer {

    /**
     * 配置定时任务线程池
     */
    @Bean("syncTaskScheduler")
    ThreadPoolTaskScheduler syncTaskScheduler() {
        ThreadPoolTaskScheduler scheduler = new ThreadPoolTaskScheduler()
        scheduler.setPoolSize(5) // 线程池大小
        scheduler.setThreadNamePrefix("sync-task-")
        scheduler.setAwaitTerminationSeconds(60)
        scheduler.setWaitForTasksToCompleteOnShutdown(true)
        scheduler.setRejectedExecutionHandler(new java.util.concurrent.ThreadPoolExecutor.CallerRunsPolicy())
        return scheduler
    }

    @Override
    void configureTasks(ScheduledTaskRegistrar taskRegistrar) {
        taskRegistrar.setScheduler(syncTaskScheduler())
    }
}

/**
 * 同步任务配置属性
 */
@ConfigurationProperties(prefix = "station.sync")
class SyncTaskProperties {
    
    /**
     * 是否启用同步任务
     */
    boolean enabled = true
    
    /**
     * 是否启用增量同步
     */
    boolean incrementalEnabled = false
    
    /**
     * 是否启用每周全量同步
     */
    boolean weeklyEnabled = true
    
    /**
     * 同步任务执行的cron表达式
     */
    String cron = "0 0 2 * * ?"
    
    /**
     * 增量同步的cron表达式
     */
    String incrementalCron = "0 0 * * * ?"
    
    /**
     * 每周全量同步的cron表达式
     */
    String weeklyCron = "0 0 1 * * SUN"
    
    /**
     * 同步超时时间（毫秒）
     */
    long timeoutMs = 300000 // 5分钟
    
    /**
     * 批处理大小
     */
    int batchSize = 100
    
    /**
     * 重试次数
     */
    int retryCount = 3
    
    /**
     * 重试间隔（毫秒）
     */
    long retryIntervalMs = 5000
}
