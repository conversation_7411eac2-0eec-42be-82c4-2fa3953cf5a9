package com.bugu.station.core.domain.site;

import com.bugu.station.core.domain.base.AbstractDefaultEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "site_config")
public class SiteConfigEntity extends AbstractDefaultEntity {

    private String title;

    @Column(name = "is_active", nullable = false, columnDefinition = "BOOLEAN DEFAULT false")
    private Boolean isActive = false;

    private String porgId;

}