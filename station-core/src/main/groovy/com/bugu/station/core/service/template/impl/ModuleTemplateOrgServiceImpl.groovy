package com.bugu.station.core.service.template.impl

import com.bugu.station.common.constant.EntityStatusEnum
import com.bugu.station.core.domain.template.ModuleTemplateOrgEntity
import com.bugu.station.core.mapstruct.template.ModuleTemplateOrgEntityVoMapper
import com.bugu.station.core.model.view.template.ModuleTemplateVO
import com.bugu.station.core.repository.template.ModuleTemplateOrgEntityRepository
import com.bugu.station.core.service.template.ModuleTemplateOrgService
import groovy.util.logging.Slf4j
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

@Slf4j
@Service
class ModuleTemplateOrgServiceImpl implements ModuleTemplateOrgService {

    @Autowired
    ModuleTemplateOrgEntityRepository moduleTemplateOrgEntityRepository

    @Autowired
    ModuleTemplateOrgEntityVoMapper moduleTemplateOrgEntityVoMapper

    @

    @Override
    List<ModuleTemplateVO> transformModuleTemplateVO(String orgId) {

        List<ModuleTemplateOrgEntity> moduleTemplateList = moduleTemplateOrgEntityRepository.findAllByOrgIdAndStatusAndIsActive(orgId, EntityStatusEnum.NORMAL.status, true)

        List<ModuleTemplateVO> moduleTemplateVOList = moduleTemplateOrgEntityVoMapper.toListVo(moduleTemplateList)
        return moduleTemplateVOList
    }
}
