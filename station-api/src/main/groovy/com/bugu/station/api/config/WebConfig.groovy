package com.bugu.station.api.config

import com.bugu.station.api.interceptor.AsyncApiInterceptor
import com.bugu.station.api.interceptor.StationInterceptor
import com.bugu.station.api.interceptor.TokenInterceptor
import com.bugu.station.api.resolver.ParamResolver
import com.bugu.station.core.service.AccessStatsService
import com.bugu.station.core.service.classification.DynamicClassificationDetailService
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.context.annotation.Configuration
import org.springframework.core.task.AsyncTaskExecutor
import org.springframework.web.method.support.HandlerMethodArgumentResolver
import org.springframework.web.servlet.config.annotation.AsyncSupportConfigurer
import org.springframework.web.servlet.config.annotation.CorsRegistry
import org.springframework.web.servlet.config.annotation.InterceptorRegistry
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer

import java.util.concurrent.Executors

@Configuration
class WebConfig implements WebMvcConfigurer {

    private final StationInterceptor stationInterceptor
    private final TokenInterceptor tokenInterceptor
    private final DynamicClassificationDetailService dynamicClassificationDetailService
    private final ParamResolver paramResolver
    private final AccessStatsService accessStatsService

    @Autowired
    WebConfig(TokenInterceptor tokenInterceptor, DynamicClassificationDetailService dynamicClassificationDetailService, ParamResolver paramResolver, AccessStatsService accessStatsService, StationInterceptor stationInterceptor) {
        this.tokenInterceptor = tokenInterceptor
        this.dynamicClassificationDetailService = dynamicClassificationDetailService
        this.paramResolver = paramResolver
        this.accessStatsService = accessStatsService
        this.stationInterceptor = stationInterceptor
    }

    private final String[] asyncPathPatterns = ["/api/stationGroup/1.0/user/dynamicClassification/detail/**"]
    private final String[] tokenExcludePathPatterns = ["/api/stationGroup/1.0/user/**", "/login", "/swagger-ui/**", "/v3/api-docs/**", "/actuator", "/error"]

    @Override
    void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(stationInterceptor)
                .order(5)
                .addPathPatterns("/**") // 拦截所有请求
                .excludePathPatterns(tokenExcludePathPatterns)

        registry.addInterceptor(tokenInterceptor)
                .order(20)
                .addPathPatterns("/**") // 拦截所有请求
                .excludePathPatterns(tokenExcludePathPatterns) // 排除不需要拦截的路径

        // 注册异步拦截器
        registry.addInterceptor(new AsyncApiInterceptor(Executors.newFixedThreadPool(10), dynamicClassificationDetailService, accessStatsService))
                .order(10)
                .addPathPatterns("/**")  // 拦截所有/api开头的路径
//                .excludePathPatterns("/api/public/**") // 排除公共API
    }

    @Override
    void configureAsyncSupport(AsyncSupportConfigurer configurer) {
        // 配置异步处理的超时时间(毫秒)
        configurer.setDefaultTimeout(5000)
        // 配置异步任务执行器
        configurer.setTaskExecutor(Executors.newFixedThreadPool(10) as AsyncTaskExecutor)
    }

    @Override
    void addArgumentResolvers(List<HandlerMethodArgumentResolver> resolvers) {
        resolvers.add(paramResolver)
    }

    @Override
    void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/api/**")
                .allowedOrigins("http://codeless.yunzhiyuan100.com", "https://daily.buguk12.com", "https://bg-digtital-edu.ts.ixaedu.cn") // 前端地址
                .allowedMethods("GET", "POST", "PUT", "DELETE", "OPTIONS")
                .allowedHeaders("*")
                .exposedHeaders("Authorization")
                .allowCredentials(true)
                .maxAge(3600)
    }
}