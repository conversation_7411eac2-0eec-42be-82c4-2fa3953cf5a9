package com.bugu.station.api.interceptor

import com.alibaba.fastjson2.JSONObject
import com.bugu.station.common.context.UserContext
import com.bugu.station.common.model.security.AuthUserDto
import groovy.util.logging.Slf4j
import io.jsonwebtoken.Claims
import io.jsonwebtoken.JwtException
import io.jsonwebtoken.Jwts
import jakarta.servlet.http.HttpServletRequest
import jakarta.servlet.http.HttpServletResponse
import org.springframework.lang.Nullable
import org.springframework.stereotype.Component
import org.springframework.web.servlet.HandlerInterceptor
import org.springframework.web.servlet.ModelAndView

@Slf4j
@Component
class TokenInterceptor implements HandlerInterceptor {

    private final String secret = "bjkN^ci6p#C&uL86xh1bm&nhekGbAfGT"

    private final String expirationTime = "86400"

    private final UserContext userContext

    TokenInterceptor(UserContext userContext) {
        this.userContext = userContext
    }
    // 白名单路径
    private static final List<String> WHITE_LIST = [
    ]

    @Override
    boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        String requestURI = request.getRequestURI()

        // 1. OPTIONS 请求直接放行
        if ("OPTIONS".equalsIgnoreCase(request.getMethod())) {
            log.debug("Skipping token check for OPTIONS request: {}", requestURI)
            return true
        }

        // 2. 白名单路径跳过 token 校验
        if (WHITE_LIST.any { requestURI.startsWith(it) }) {
            log.debug("Skipping token check for whitelisted path: {}", requestURI)
            return true
        }

        // 3. 获取 Authorization 头
        String authHeader = request.getHeader("Authorization")
        if (authHeader == null || !authHeader.startsWith("Bearer ")) {
            log.warn("Missing or invalid token in request: {}", requestURI)
            response.sendError(HttpServletResponse.SC_UNAUTHORIZED, "Missing or invalid token")
            return false
        }

        // 4. 提取 token
        String token = authHeader.substring(7)
        // 5. 解析 token
        Claims claims
        try {
            claims = getAllClaimsFromToken(token)
            if (claims.getExpiration().before(new Date())) {
                log.info("Token expired for request: {}", requestURI)
                response.sendError(HttpServletResponse.SC_UNAUTHORIZED, "Token expired")
                return false
            }
        } catch (JwtException | IllegalArgumentException e) {
            log.error("Invalid token in request: {}, error: {}", requestURI, e.getMessage(), e)
            response.sendError(HttpServletResponse.SC_UNAUTHORIZED, "Invalid token")
            return false
        }

        // 6. 解析用户信息
        try {
            JSONObject user = JSONObject.parseObject(claims.getIssuer())
            String userId = user.getString("userId")
            String organizationId = user.getString("organizationId")
            Byte userType = user.getByte("userType")
            String userName = user.getString("userName")

            AuthUserDto authUserDto = new AuthUserDto(userId, userType, userName)
            request.setAttribute("authUser", authUserDto)
//            request.setAttribute("orgId", organizationId)
            userContext.setCurrentUser(authUserDto)

            log.debug("Authenticated user: {} (ID: {}, Type: {}) for request: {}", userName, userId, userType, requestURI)
        } catch (Exception e) {
            log.error("Failed to parse user info from token in request: {}, error: {}", requestURI, e.getMessage(), e)
            response.sendError(HttpServletResponse.SC_UNAUTHORIZED, "Invalid token payload")
            return false
        }

        return true
    }

    @Override
    void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler, @Nullable ModelAndView modelAndView) throws Exception {
        super.postHandle(request, response, handler, modelAndView)
    }

    @Override
    void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, @Nullable Exception ex) throws Exception {
        userContext.clear()
        super.afterCompletion(request, response, handler, ex)
    }

    boolean isValidToken(String token) {
        try {
            final Date expiration = getAllClaimsFromToken(token).getExpiration()
            return expiration.before(new Date())
        } catch (JwtException | IllegalArgumentException e) {
            return false
        }
    }

    Claims getAllClaimsFromToken(String token) {
        return Jwts.parser()
                .setSigningKey(Base64.getEncoder().encodeToString(secret.getBytes()))
                .parseClaimsJws(token)
                .getBody()
    }

}
