package com.bugu.station.api.task

import com.bugu.station.core.task.SynsTaskService
import groovy.util.logging.Slf4j
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Component


/**
 * @Author: sjl
 * @CreateTime: 2025-08-04
 * @Description: 组织站点信息同步定时任务
 * @Version: 1.0
 */
@Slf4j
@Component
class SynsTask {

    @Autowired
    private SynsTaskService synsTaskService


    /**
     * 每日凌晨1点执行全量同步
     */
    @Scheduled(cron = "0 0 1 * * *")
    void weeklyFullSynOrg2Site() {
        log.info("=== 开始执行每日全量同步组织站点信息任务 ===")

        long startTime = System.currentTimeMillis()

        try {
            synsTaskService.synOrg2Site()

            long duration = System.currentTimeMillis() - startTime
            log.info("=== 每周全量同步组织站点信息任务执行完成，耗时: {} ms ===", duration)

        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime
            log.error("=== 每周全量同步组织站点信息任务执行失败，耗时: {} ms ===", duration, e)
        }
    }
}
