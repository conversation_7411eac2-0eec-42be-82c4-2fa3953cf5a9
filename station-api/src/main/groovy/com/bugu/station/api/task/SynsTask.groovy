package com.bugu.station.api.task

import com.bugu.station.core.task.SynsTaskService
import groovy.util.logging.Slf4j
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Component


/**
 * @Author: sjl
 * @CreateTime: 2025-08-04
 * @Description: 组织站点信息同步定时任务
 * @Version: 1.0
 */
@Slf4j
@Component
@ConditionalOnProperty(name = "station.sync.enabled", havingValue = "true", matchIfMissing = true)
class SynsTask {

    @Autowired
    private SynsTaskService synsTaskService

    /**
     * 每天凌晨2点执行组织站点信息同步
     * cron表达式: 秒 分 时 日 月 周
     */
    @Scheduled(cron = "0 0 2 * * ?")
    void synOrg2Site() {
        log.info("=== 开始执行定时同步组织站点信息任务 ===")

        long startTime = System.currentTimeMillis()

        try {
            synsTaskService.synOrg2Site()

            long duration = System.currentTimeMillis() - startTime
            log.info("=== 定时同步组织站点信息任务执行完成，耗时: {} ms ===", duration)

        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime
            log.error("=== 定时同步组织站点信息任务执行失败，耗时: {} ms ===", duration, e)
        }
    }

    /**
     * 每小时执行一次增量同步（可选）
     * 用于同步最近更新的组织信息
     */
    @Scheduled(cron = "0 0 * * * ?")
    @ConditionalOnProperty(name = "station.sync.incremental.enabled", havingValue = "true", matchIfMissing = false)
    void incrementalSynOrg2Site() {
        log.info("=== 开始执行增量同步组织站点信息任务 ===")

        long startTime = System.currentTimeMillis()

        try {
            // 这里可以实现增量同步逻辑
            // 例如：只同步最近1小时内更新的组织
            synsTaskService.synOrg2Site()

            long duration = System.currentTimeMillis() - startTime
            log.info("=== 增量同步组织站点信息任务执行完成，耗时: {} ms ===", duration)

        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime
            log.error("=== 增量同步组织站点信息任务执行失败，耗时: {} ms ===", duration, e)
        }
    }

    /**
     * 每周日凌晨1点执行全量同步
     * 确保数据的完整性和一致性
     */
    @Scheduled(cron = "0 0 1 * * SUN")
    @ConditionalOnProperty(name = "station.sync.weekly.enabled", havingValue = "true", matchIfMissing = true)
    void weeklyFullSynOrg2Site() {
        log.info("=== 开始执行每周全量同步组织站点信息任务 ===")

        long startTime = System.currentTimeMillis()

        try {
            synsTaskService.synOrg2Site()

            long duration = System.currentTimeMillis() - startTime
            log.info("=== 每周全量同步组织站点信息任务执行完成，耗时: {} ms ===", duration)

        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime
            log.error("=== 每周全量同步组织站点信息任务执行失败，耗时: {} ms ===", duration, e)
        }
    }
}
