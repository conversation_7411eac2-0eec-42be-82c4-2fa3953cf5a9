package com.bugu.station.api.controller.admin

import com.bugu.result.Result2VO
import com.bugu.station.core.config.Views
import com.bugu.station.core.task.SynsTaskService
import com.fasterxml.jackson.annotation.JsonView
import groovy.util.logging.Slf4j
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.*

/**
 * @Title: SyncTaskController
 * @Author: [sjl]
 * @Package com.bugu.station.api.controller.admin
 * @Date: 2025-08-04
 * @Description: 同步任务管理控制器
 * @Version: 1.0
 */
@Slf4j
@JsonView(Views.Admin.class)
@RestController
@RequestMapping("/api/stationGroup/1.0/admin/sync")
@Tag(name = "同步任务管理接口（Admin）", description = "提供组织站点信息同步任务的手动触发和管理功能")
class SyncTaskController {

    @Autowired
    private SynsTaskService synsTaskService

    /**
     * 手动触发组织站点信息同步
     */
    @PostMapping("/orgtosite")
    @Operation(summary = "手动同步组织站点信息", 
               description = "手动触发组织站点信息同步任务，将用户模块的组织信息同步到本地站点配置",
               responses = [
                   @ApiResponse(responseCode = "200", description = "同步任务执行成功"),
                   @ApiResponse(responseCode = "500", description = "同步任务执行失败")
               ])
    Result2VO syncOrgToSite() {
        log.info("=== 手动触发组织站点信息同步任务 ===")
        
        long startTime = System.currentTimeMillis()
        
        try {
            // 执行同步任务
            synsTaskService.synOrg2Site()
            
            long duration = System.currentTimeMillis() - startTime
            log.info("=== 手动同步任务执行完成，耗时: {} ms ===", duration)
            
            return Result2VO.success([
                message: "组织站点信息同步任务执行成功",
                duration: duration,
                timestamp: new Date()
            ])
            
        } catch (Exception ignored) {
            return Result2VO.failure()
        }
    }


}
