package com.bugu.station.app

import com.bugu.station.oss.properties.OssProperties
import org.springframework.boot.SpringApplication
import org.springframework.boot.autoconfigure.SpringBootApplication
import org.springframework.boot.context.properties.EnableConfigurationProperties
import org.springframework.cache.annotation.EnableCaching
import org.springframework.data.jpa.repository.config.EnableJpaAuditing
import org.springframework.scheduling.annotation.EnableScheduling

//@EntityScan("com.bugu.station.core.domain")
//@EnableJpaRepositories("com.bugu.station.core.repository")
@EnableCaching
@EnableJpaAuditing
@EnableScheduling
@EnableConfigurationProperties(OssProperties.class)
@SpringBootApplication(scanBasePackages = ["com.bugu.station"])
class StationAppApplication {

    static void main(String[] args) {
        SpringApplication.run(StationAppApplication, args)
    }

}
