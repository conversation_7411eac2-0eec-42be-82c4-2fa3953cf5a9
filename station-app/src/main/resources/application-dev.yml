spring:
  config:
    import: "optional:configserver:"
  mvc:
    cors:
      # 必须指定具体前端域名（不能是 *，因为 allow-credentials=true）
      allowed-origins:
        - http://codeless.yunzhiyuan100.com
        - https://daily.buguk12.com
      allowed-methods:
        - GET
        - POST
        - PUT
        - DELETE
        - OPTIONS
      allowed-headers: "*"  # 或明确列出：Content-Type, Authorization
      allow-credentials: true
      exposed-headers:
        - Authorization  # 可选：暴露自定义头
        - Content-Type
        - X-Requested-With
      max-age: 3600
  datasource:
    station-group:
      jdbc-url: **************************************************************************************************************************************************
      username: bugu
      password: Im8nb1qaz2wsx
      schema: station_group
      driver-class-name: com.mysql.cj.jdbc.Driver
      connection-init-sql: SELECT 1  # 连接初始化SQL
      connection-test-query: SELECT 1 # 连接测试查询
      initialization-fail-timeout: 1 # 初始化失败超时(毫秒)
      maximum-pool-size: 10         # 最大连接池数量
      minimum-idle: 2               # 最小空闲连接数
      idle-timeout: 30000           # 空闲连接超时时间（毫秒）
      max-lifetime: 1800000         # 连接最大存活时间（毫秒），建议小于数据库的 wait_timeout
      connection-timeout: 30000     # 获取连接的最大等待时间
      pool-name: MyHikariPool       # 自定义连接池名称
      auto-commit: true             # 是否自动提交
      validation-timeout: 5000      # 验证连接的超时时间
      leak-detection-threshold: 60000  # 连接泄露检测时间（毫秒）

  jpa:
    #    database-platform: org.hibernate.dialect.MySQLDialect
    show-sql: false
    properties:
      hibernate:
        jdbc.batch_size: 20
        order_inserts: true
        order_updates: true
        generate_statistics: false
    hibernate:
      ddl-auto: update
      use-new-id-generator-mappings: true
  data:
    redis:
      repositories:
        enabled: false
      host: ${redis.host}
      password: ${redis.password}
      port: ${redis.port}
      database: 0
      lettuce:
        pool:
          max-active: 8
          max-idle: 8
          min-idle: 0
---
logging:
  level:
    org.hibernate.SQL: WARN
    org.hibernate.type.descriptor.sql.BasicBinder: WARN
---
#本地文件模块
station-oss:
  type: aliOss
  file-prefix: station-group
  aliOss:
    endpoint: oss-cn-hangzhou.aliyuncs.com
    access-key-id: LTAIvKscF8qNTVzZ
    access-key-secret: U23XZSEw4uWvgHdVMEp8HbJrW7dAwV
    bucket-name: product360
    open-bucket-name: bugutech
---
redis:
  host: redis.yunzhiyuan100.com.cn
  password: Huawei@123
  port: 6079
---
soda:
  enable: true
  findHtmlUrl: https://soda-admin.yunzhiyuan100.com/api/soda/1.0/manager/cloud/htmlConfig
  replace-first:
    - /district
    - /school
    - /department
---
jetcache:
  statIntervalMinutes: 15
  areaInCacheName: false
  local:
    default:
      type: linkedhashmap
      keyConvertor: fastjson2
  remote:
    default:
      type: redisson
      redissonClient: redisClientBeanName
      keyConvertor: fastjson2
      valueEncoder: kryo5
      valueDecoder: kryo5
      keyPrefix: spring-data-redis

---
nacos:
  #host: mse-09280d20-nacos-ans.mse.aliyuncs.com
  host: mse-09280d20-p.nacos-ans.mse.aliyuncs.com

---
# Spring Cloud Nacos 配置
spring:
  cloud:
    nacos:
      discovery:
        server-addr: ${nacos.host}:8848
        enabled: true
      config:
        server-addr: ${nacos.host}:8848
        enabled: false  # 如果不使用Nacos配置中心，设置为false
        import-check:
          enabled: false  # 禁用导入检查

---
dubbo:
  application:
    name: ${spring.application.name}
    register-mode: all
    qos-enable: true
    qos-accept-foreign-ip: false
  #  scan:
  #    base-packages:
  #      - bgb.core.user.repository
  protocol:
    name: dubbo
    port: -1
  #    serialization: fastjson
  registry:
    address: nacos://${nacos.host}:8848
    use-as-metadata-center: true
    check: false
    register: true
    parameters:
      namespace: 6cf80985-6033-492e-ab90-021ca9abdda5
  cloud:
    #指定需要订阅的服务提供方，默认值*，会订阅所有服务，不建议使用
    subscribed-services: ${spring.application.name}, user-module
  consumer:
    timeout: 60000
    retries: 0
    init: true
    check: false
  #    serialization: fastjson
  provider:
    threads: 300
    queues: 200