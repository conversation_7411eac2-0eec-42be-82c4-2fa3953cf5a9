spring:
  mvc:
    cors:
      enabled: true
      allowed-origins:
        - http://codeless.yunzhiyuan100.com
        - https://daily.buguk12.com
      allowed-methods:
        - GET
        - POST
        - PUT
        - DELETE
        - OPTIONS
      allowed-headers: "*"
      allow-credentials: true
      exposed-headers:
        - Authorization
        - Content-Type
        - X-Requested-With
      max-age: 3600
  datasource:
    station-group:
      jdbc-url: **************************************************************************************************************************************************
      username: bugu
      password: Im8nb1qaz2wsx
      #      jdbc-url: **************************************************************************************************************
      #      username: root
      #      password: R00tR@@t
      schema: station_group
      driver-class-name: com.mysql.cj.jdbc.Driver
      #      type: com.zaxxer.hikari.HikariDataSource
      connection-init-sql: SELECT 1
      maximum-pool-size: 20
      minimum-idle: 2
      idle-timeout: 30000
      max-lifetime: 1800000
      connection-timeout: 30000
      pool-name: MyHikariPool123
      leak-detection-threshold: 60000
  #      hikari:
  #        schema: station_group
  #        connection-init-sql: SELECT 1
  #        maximum-pool-size: 20
  #        minimum-idle: 2
  #        idle-timeout: 30000
  #        max-lifetime: 1800000
  #        connection-timeout: 30000
  #        pool-name: MyHikariPool
  #        leak-detection-threshold: 60000

  jpa:
    #    database-platform: org.hibernate.dialect.MySQLDialect
    show-sql: true
    properties:
      hibernate:
        jdbc.batch_size: 20
        order_inserts: true
        order_updates: true
        generate_statistics: false
    hibernate:
      ddl-auto: update
      use-new-id-generator-mappings: true
  data:
    redis:
      repositories:
        enabled: false
      host: ${redis.host}
      password: ${redis.password}
      port: ${redis.port}
      database: 0
      lettuce:
        pool:
          max-active: 8
          max-idle: 8
          min-idle: 0
---
logging:
  level:
    com.zaxxer.hikari: WARN
    org.springframework.web: DEBUG
  threshold:
    console: debug
---
#本地文件模块
station-oss:
  type: aliOss
  aliOss:
    endpoint: oss-cn-hangzhou.aliyuncs.com
    endpoint_view: oss-cn-hangzhou.aliyuncs.com
    access-key-id: LTAIvKscF8qNTVzZ
    access-key-secret: U23XZSEw4uWvgHdVMEp8HbJrW7dAwV
    bucket-name: product360
    open-bucket-name: bugutech
---
redis:
  host: redis.yunzhiyuan100.com.cn
  password: Huawei@123
  port: 6079
---
soda:
  enable: true
  findHtmlUrl: https://soda-admin.yunzhiyuan100.com/api/soda/1.0/manager/cloud/htmlConfig
  replace-first:
    - /district
    - /school
    - /department
  type: LOCAL_DB
---
jetcache:
  statIntervalMinutes: 15
  areaInCacheName: false
  local:
    default:
      type: linkedhashmap
      keyConvertor: fastjson2
  remote:
    default:
      type: redisson
      redissonClient: redisClientBeanName
      keyConvertor: fastjson2
      valueEncoder: kryo5
      valueDecoder: kryo5
      keyPrefix: spring-data-redis
---
server:
  undertow:
    options:
      DISABLE_CSRF_PROTECTION: true  # 仅在开发环境使用，生产环境应妥善配置CSRF保护