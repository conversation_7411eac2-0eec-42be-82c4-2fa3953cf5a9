spring:
  application:
    name: station-app
  jackson:
    default-property-inclusion: non_null
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB
#  config:
#    activate:
#      on-profile: dev

---
# 站点同步任务配置
station:
  sync:
    lesseeId: 1953045001384693760
  daliyUrl: https://daily.buguk12.com/bd/station-web/
---
# Spring Boot Actuator 监控配置
management:
  endpoints:
    web:
      exposure:
        include: "*"         # 开启所有端点（生产环境建议按需开启）
  endpoint:
    health:
      show-details: always   # 显示详细健康信息
  health:
    redis:
      enabled: off
---
server:
  port: 18300