buildscript {
    repositories {
        mavenLocal()
//        mavenCentral()
        maven { url "https://maven.aliyun.com/repository/gradle-plugin" }
        maven { url "https://maven.aliyun.com/repository/grails-core" }
        maven {
            credentials {
                username '5ee057b297528f50b957a24b'
                password '5EDW9X0yEh(z'
            }
            url 'https://packages.aliyun.com/maven/repository/2031436-release-DZ8BKk/'
        }
        maven {
            credentials {
                username '5ee057b297528f50b957a24b'
                password '5EDW9X0yEh(z'
            }
            url 'https://packages.aliyun.com/maven/repository/2031436-snapshot-ROy4tH/'
        }
    }
}
plugins {
    id 'groovy'
    id 'org.springframework.boot' version '3.5.3'
    id 'io.spring.dependency-management' version '1.1.7'
}

group = 'com.bugu'
version = '0.0.1-SNAPSHOT'

java {
    toolchain {
        languageVersion = JavaLanguageVersion.of(17)
    }
}

repositories {
//    mavenLocal()
    maven { url "https://maven.aliyun.com/repository/jcenter" }
    maven { url "https://maven.aliyun.com/repository/public" }
    maven { url "https://maven.aliyun.com/repository/google" }
    maven { url "https://maven.aliyun.com/repository/gradle-plugin" }
    maven { url "https://maven.aliyun.com/repository/spring" }
    maven { url "https://maven.aliyun.com/repository/spring-plugin" }
    maven {
        credentials {
            username '5ee057b297528f50b957a24b'
            password '5EDW9X0yEh(z'
        }
        url 'https://packages.aliyun.com/maven/repository/2031436-release-DZ8BKk/'
    }
    maven {
        credentials {
            username '5ee057b297528f50b957a24b'
            password '5EDW9X0yEh(z'
        }
        url 'https://packages.aliyun.com/maven/repository/2031436-snapshot-ROy4tH/'
    }
    mavenCentral()
}

ext {
    set('springCloudVersion', "2025.0.0")
    set("springCloudAlibabaVersion", "2023.0.3.3")
    set("dubboSpringBootStarterVersion", "3.2.14")
}

dependencyManagement {
    imports {
        mavenBom "org.springframework.cloud:spring-cloud-dependencies:${springCloudVersion}"
        mavenBom "com.alibaba.cloud:spring-cloud-alibaba-dependencies:${springCloudAlibabaVersion}"
    }
}

dependencies {

    implementation project(':station-api')
    implementation project(':station-oss')
    implementation project(':station-codeless')
//    implementation project(':station-core')
    implementation project(':station-common')
    implementation 'jakarta.annotation:jakarta.annotation-api:3.0.0'

    implementation 'org.springframework.boot:spring-boot-starter'
    implementation "org.springframework.boot:spring-boot-autoconfigure"
    implementation('org.springframework.boot:spring-boot-starter-web') {
        exclude group: 'org.springframework.boot', module: 'spring-boot-starter-tomcat'
    }
    implementation 'org.springframework.boot:spring-boot-starter-actuator'
    implementation 'org.springframework.boot:spring-boot-starter-data-jpa'
    implementation "org.springframework.boot:spring-boot-starter-undertow"
    implementation "org.springframework.boot:spring-boot-starter-data-redis"
    implementation 'org.redisson:redisson-spring-boot-starter'
    implementation 'com.alicp.jetcache:jetcache-starter-redisson:2.7.8'

    // Dubbo和Nacos相关依赖
    implementation "org.apache.dubbo:dubbo-spring-boot-starter:${dubboSpringBootStarterVersion}"
    implementation 'com.alibaba.cloud:spring-cloud-starter-alibaba-nacos-discovery'
    implementation 'com.alibaba.cloud:spring-cloud-starter-alibaba-nacos-config'

    implementation 'org.apache.groovy:groovy'
    runtimeOnly 'com.mysql:mysql-connector-j'
    runtimeOnly 'org.springframework.boot:spring-boot-devtools'
    implementation 'jakarta.persistence:jakarta.persistence-api:3.1.0'
    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    testRuntimeOnly 'org.junit.platform:junit-platform-launcher'
}

test {
    useJUnitPlatform()
}
//tasks.withType(JavaCompile).configureEach {
//    options.compilerArgs += '-parameters'
//}

//bootJar {
////    enabled = false
//    duplicatesStrategy = DuplicatesStrategy.EXCLUDE
//    mainClass = 'com.bugu.receiver.ReceiverCoreApplication'
//}

jar {
    enabled = true
}
