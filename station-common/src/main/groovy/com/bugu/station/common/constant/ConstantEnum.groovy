package com.bugu.station.common.constant

class ConstantEnum {


    static enum SimplePresentationType {

    }


    static  enum NavigationType{
        BANNER(1,"banner"),
        NAVIGATION(2,"navigation"),
        ;

         int type;
         String name;

        NavigationType(int type, String name) {
            this.type = type;
            this.name = name;
        }
    }


     enum OrganizationType {
        PJ((byte) 1, "普教"),
        GZX((byte) 11, "高职校"),

        SJYT((byte) 17, "省教育厅"),
        SJYTZSDW((byte) 18, "省教育厅直属单位"),
        SQSJYJ((byte) 19, "设区市教育局"),
        SQSJYJZSDW((byte) 20, "设区市教育局直属单位"),
        XSQJYJ((byte) 21, "县（市、区）教育局"),
        XSQJYJZSDW((byte) 22, "县（市、区）教育局直属单位"),
        QTJYXZJG((byte) 23, "其他教育行政机构");

         byte type;
         String name;

        OrganizationType(byte type, String name) {
            this.type = type;
            this.name = name;
        }

         static OrganizationType getEnumByType(Byte type) {
            for (OrganizationType s : OrganizationType.values()) {
                if (Objects.equals(s.type, type)) {
                    return s;
                }
            }
            return null;

        }

         static OrganizationType getEnumByName(String name) {
            for (OrganizationType s : OrganizationType.values()) {
                if (Objects.equals(s.name, name)) {
                    return s;
                }
            }
            return null;

        }

    }


     enum OrganizationCategory {
        SCHOOL((byte) 1, "学校"),
        EDUCATIONAL((byte) 2, "教育行政机构");

         byte type;
         String name;

        OrganizationCategory(byte type, String name) {
            this.type = type;
            this.name = name;
        }

         static OrganizationCategory getEnumByType(Byte type) {
            for (OrganizationCategory s : OrganizationCategory.values()) {
                if (Objects.equals(s.type, type)) {
                    return s;
                }
            }
            return null;

        }

         static OrganizationCategory getEnumByName(String name) {
            for (OrganizationCategory s : OrganizationCategory.values()) {
                if (Objects.equals(s.name, name)) {
                    return s;
                }
            }
            return null;

        }
    }

    enum SiteConfigTypeEnum {
        CITYSTATION(1,"市级站点"),
        DISTRICT_LEVEL_STATION(2,"区县级站点"),
        SCHOOL_STATION(3,"学校站点");

        int type;
        String name;

        SiteConfigTypeEnum(int type, String name) {
            this.type = type;
            this.name = name;
        }
    }


}
